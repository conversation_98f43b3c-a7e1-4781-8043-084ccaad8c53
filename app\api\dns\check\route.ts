/*
 * @Author: zhouming <EMAIL>
 * @Date: 2025-08-26 11:25:09
 * @LastEditors: zhouming <EMAIL>
 * @LastEditTime: 2025-09-29 14:40:01
 * @FilePath: \wp-sitemgr\app\api\dns\check\route.ts
 * @Description: 
 * 
 * Copyright (c) 2025 by zhixin tech, All Rights Reserved. 
 */
import { NextResponse } from 'next/server';
import { dnsConfig } from '@/app/lib/config';


// POST /api/dns/check - Check DNS resolution
export async function POST(request: Request) {
  let nameDomain: string = '';
  try {
    let resolved = false;
    let records: String[] = [];
    let msg = '';
    const requestData = await request.json();
    nameDomain = requestData.domain;

    if (!nameDomain) {
      return NextResponse.json(
        { error: 'Domain is required' },
        { status: 400 }
      );
    }
    try {
      const result = await fetch(`${dnsConfig.dnsBaseServerApi}/api/cfcheck`, {
        method: 'POST',
        headers: {
          'auth-header': `${process.env.DNS_MANAGER_API_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ nameDomain: nameDomain })
      });
      if (result.status != 200) {
        throw new Error(`请求解析接口失败 ${result.status}`);
      }

      resolved = true;
      const a = await result.json();
      console.log(a);
      records = (await result.json()).records || [];
    } catch (error) {
      resolved = false;
      records = [];
      msg = error.message;
    }
    return NextResponse.json({
      domain: nameDomain,
      resolved,
      records,
      message: resolved ? 'Domain is resolved' : 'Domain is not resolved'
    });
  } catch (error) {
    console.error('DNS check error:', error);
    if (error instanceof Error && 'code' in error && error.code === 'ENOTFOUND') {
      return NextResponse.json({
        domain: nameDomain,
        resolved: false,
        records: [],
        message: 'Domain is not resolved'
      });
    }
    return NextResponse.json(
      { error: 'Failed to check DNS resolution' },
      { status: 500 }
    );
  }
}
